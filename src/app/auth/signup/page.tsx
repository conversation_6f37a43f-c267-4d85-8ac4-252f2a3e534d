'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { TabSelector } from '@/shared/components/ui';
import { SignupClientForm, SignupBarberForm, SignupShopOwnerForm, SIGNUP_TABS, AUTH_ROUTES } from '@/features/auth';
import type { UserRole } from '@/types/auth';

export default function SignupPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<UserRole>('client');

  const tabs = SIGNUP_TABS;

  const handleBack = () => {
    router.push(AUTH_ROUTES.HOME);
  };

  const renderForm = () => {
    switch (activeTab) {
      case 'client':
        return <SignupClientForm />;
      case 'barber':
        return <SignupBarberForm />;
      case 'shop_owner':
        return <SignupShopOwnerForm />;
      default:
        return null;
    }
  };

  const heroContent = (
    <div className="space-y-4 lg:space-y-6">
      <div className="text-center">
        <h2 className="text-white text-3xl lg:text-4xl font-bold mb-4 lg:mb-6">Register</h2>
      </div>
      <TabSelector
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={(tabId) => setActiveTab(tabId as UserRole)}
      />
    </div>
  );

  return (
    <AuthLayout
      showBackButton={true}
      onBack={handleBack}
      heroContent={heroContent}
      contentType="complex"
    >
      <div className="space-y-6">
        {/* Form Content */}
        {renderForm()}
      </div>
    </AuthLayout>
  );
}
