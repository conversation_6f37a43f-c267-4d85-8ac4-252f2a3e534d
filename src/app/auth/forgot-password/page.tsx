"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AuthLayout } from "@/shared/components/layout";
import { Button, Input } from "@/shared/components/ui";
import { useForgotPassword } from "@/features/auth/hooks/use-forgot-password";
import { AUTH_ROUTES, AUTH_LOADING_MESSAGES } from "@/features/auth/constants";

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const { requestPasswordReset, isLoading, error, success, clearMessages } =
    useForgotPassword();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    // Clear messages when user starts typing
    if (error || success) {
      clearMessages();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      return;
    }

    try {
      await requestPasswordReset(email);
      // On success, navigate to OTP page with email parameter
      router.push(`${AUTH_ROUTES.OTP}?email=${encodeURIComponent(email)}`);
    } catch (err) {
      // Error is handled by the hook
      console.error("Failed to send password reset OTP:", err);
    }
  };

  const handleBack = () => {
    router.push(AUTH_ROUTES.LOGIN);
  };

  return (
    <AuthLayout
      title="Forgot Password"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md text-sm">
            {success}
          </div>
        )}

        <Input
          type="email"
          name="email"
          placeholder="Enter you Email"
          value={email}
          onChange={handleInputChange}
          required
          icon={
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          }
          label="Email"
        />

        <Button
          type="submit"
          className="w-full"
          size="lg"
          disabled={isLoading || !email.trim()}
        >
          {isLoading ? AUTH_LOADING_MESSAGES.SENDING_OTP : "Send OTP"}
        </Button>
      </form>
    </AuthLayout>
  );
}
