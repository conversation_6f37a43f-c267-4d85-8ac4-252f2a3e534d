"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AuthLayout } from "@/shared/components/layout";
import { OTPVerification } from "@/shared/components/ui";
import { useForgotPassword } from "@/features/auth/hooks/use-forgot-password";
import { AUTH_ROUTES } from "@/features/auth/constants";

function OTPContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState("");

  const { resendPasswordResetOTP, isLoading, error, success } =
    useForgotPassword();

  // Get email from URL params
  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    } else {
      // If no email, redirect back to forgot password
      router.push(AUTH_ROUTES.FORGOT_PASSWORD);
    }
  }, [searchParams, router]);

  const handleVerify = async (otp: string) => {
    // Navigate to reset password page with email and OTP
    router.push(
      `${AUTH_ROUTES.RESET_PASSWORD}?email=${encodeURIComponent(
        email
      )}&otp=${otp}`
    );
  };

  const handleResend = async () => {
    await resendPasswordResetOTP(email);
  };

  const handleBack = () => {
    router.push(AUTH_ROUTES.FORGOT_PASSWORD);
  };

  return (
    <AuthLayout
      title="Enter OTP"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <OTPVerification
        email={email}
        onVerify={handleVerify}
        onResend={handleResend}
        title="Enter OTP"
        description={`Enter the 6-digit OTP sent to ${email}`}
        verifyButtonText="Verify"
        successTitle="OTP Verified!"
        successButtonText="Continue"
        isResending={isLoading}
        error={error || success || undefined}
      />
    </AuthLayout>
  );
}

export default function OTPPage() {
  return (
    <Suspense fallback={<div className="text-center">Loading...</div>}>
      <OTPContent />
    </Suspense>
  );
}
