"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AuthLayout } from "@/shared/components/layout";
import { Button, Input } from "@/shared/components/ui";
import { useAuth } from "@/features/auth/hooks/use-auth";
import Image from "next/image";
import { AUTH_ROUTES, AUTH_LOADING_MESSAGES } from "@/features/auth/constants";

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Clear error when user starts typing
    if (error) {
      clearError();
    }

    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await login(formData);
    } catch (err) {
      // Error is handled by the useAuth hook
      console.error("Login error:", err);
    }
  };

  const handleForgotPassword = () => {
    router.push(AUTH_ROUTES.FORGOT_PASSWORD);
  };

  const handleGoogleSignIn = () => {
    // Handle Google sign-in logic here
    console.log("Google sign-in clicked");
  };

  const handleBack = () => {
    router.push(AUTH_ROUTES.HOME);
  };

  const handleSignupRedirect = () => {
    router.push(AUTH_ROUTES.SIGNUP);
  };

  return (
    <AuthLayout
      title="Login"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        <Input
          type="email"
          name="email"
          placeholder="Enter you Email"
          value={formData.email}
          onChange={handleInputChange}
          required
          icon={
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          }
          label="Email"
        />

        <Input
          type={showPassword ? "text" : "password"}
          name="password"
          placeholder="• • • • • • • •"
          value={formData.password}
          onChange={handleInputChange}
          required
          icon={
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          }
          rightIcon={
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="hover:text-gray-600"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {showPassword ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                )}
              </svg>
            </button>
          }
          label="Password"
        />

        <div className="text-right">
          <button
            type="button"
            onClick={handleForgotPassword}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Forgot Password?
          </button>
        </div>

        <Button type="submit" className="w-full" size="lg" disabled={isLoading}>
          {isLoading ? AUTH_LOADING_MESSAGES.LOGGING_IN : "Login"}
        </Button>

        <div className="text-center">
          <span className="text-gray-500 text-sm">or Login with</span>
        </div>

        <Button
          type="button"
          variant="outline"
          onClick={handleGoogleSignIn}
          className="w-full flex items-center justify-center gap-3"
          size="lg"
        >
          <Image
            src="/assets/images/google-logo.svg"
            alt="Google"
            width={20}
            height={20}
          />
          Sign in with Google
        </Button>

        <div className="text-center">
          <span className="text-gray-600 text-sm">
            Don&apos;t have an account?
            <button
              type="button"
              onClick={handleSignupRedirect}
              className="text-amber-600 hover:text-amber-700 font-medium"
            >
              Create an Account
            </button>
          </span>
        </div>
      </form>
    </AuthLayout>
  );
}
