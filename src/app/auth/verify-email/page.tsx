'use client';

import { useRouter } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { AUTH_ROUTES, VerifyEmail } from '@/features/auth';

export default function VerifyEmailPage() {
  const router = useRouter();

  const handleBack = () => {
    router.push(AUTH_ROUTES.SIGNUP);
  };

  const handleVerificationSuccess = () => {
    // After successful signup, direct all user types to /dashboard
    router.push(AUTH_ROUTES.DASHBOARD);
  };

  return (
    <AuthLayout
      title="Verify Email"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <VerifyEmail onSuccess={handleVerificationSuccess} />
    </AuthLayout>
  );
}
