import Link from "next/link";
import { AuthLayout } from "@/shared/components/layout";
import { Button } from "@/shared/components/ui";
import { AUTH_ROUTES } from "@/features/auth/constants";

export default function Home() {
  return (
    <AuthLayout variant="home">
      <div className="space-y-4 mt-8">
        <div>
          <Link href={AUTH_ROUTES.SIGNUP}>
            <Button className="w-full" size="lg">
              Create an Account
            </Button>
          </Link>
        </div>
        <div>
          <Link href={AUTH_ROUTES.LOGIN}>
            <Button variant="outline" className="w-full" size="lg">
              Login
            </Button>
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
