'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { OTPVerification } from '@/shared/components/ui';
import { authApiService } from '../services/auth-api';

interface VerifyEmailProps {
  onSuccess?: () => void;
}

function VerifyEmailContent({ onSuccess }: VerifyEmailProps) {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';
  const sendOtp = searchParams.get('sendOtp') === 'true';

  const handleVerify = async (otp: string) => {
    const authResponse = await authApiService.verifyEmailOTP({
      email,
      otp,
    });
    console.log('Email verification successful:', authResponse);
  };

  const handleResend = async () => {
    await authApiService.resendVerificationOTP(email);
  };

  return (
    <OTPVerification
      email={email}
      onVerify={handleVerify}
      onResend={handleResend}
      title="Enter Verification Code"
      description={`We sent a code to ${email}`}
      successTitle="Email Verified!"
      onSuccess={onSuccess}
      autoSendOtp={sendOtp}
    />
  );
}

export function VerifyEmail({ onSuccess }: VerifyEmailProps) {
  return (
    <Suspense fallback={<div className="text-center">Loading...</div>}>
      <VerifyEmailContent onSuccess={onSuccess} />
    </Suspense>
  );
}
