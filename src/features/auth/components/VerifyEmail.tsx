'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button, SuccessModal, OTPInput } from '@/shared/components/ui';
import { authApiService } from '../services/auth-api';
import { useResendTimer } from '../hooks/use-resend-timer';
import { AUTH_LOADING_MESSAGES } from '../constants';

interface VerifyEmailProps {
  onSuccess?: () => void;
}

function VerifyEmailContent({ onSuccess }: VerifyEmailProps) {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';
  const sendOtp = searchParams.get('sendOtp') === 'true';

  const [code, setCode] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState<string>('');

  // Use the shared resend timer hook
  const { timeLeft, canResend, startTimer } = useResendTimer(60);

  // Auto-send OTP when coming from login
  useEffect(() => {
    if (sendOtp && email) {
      handleResend();
    }
  },);

  // Start timer on component mount if coming from login
  useEffect(() => {
    if (sendOtp && email) {
      startTimer(60);
    }
  }, [sendOtp, email, startTimer]);

  const handleOTPComplete = (otpValue: string) => {
    setCode(otpValue);
    setError(''); // Clear error when user types
  };

  const handleVerify = async () => {
    if (code.length !== 6) return;

    setIsVerifying(true);
    setError('');

    try {
      const authResponse = await authApiService.verifyEmailOTP({
        email,
        otp: code,
      });

      // Success - verification completed
      console.log('Email verification successful:', authResponse);
      setShowSuccessModal(true);

    } catch (error) {
      console.error('Verification error:', error);
      if (error instanceof Error) {
        setError(error.message || 'Verification failed');
      } else {
        setError('Network error. Please try again.');
      }
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResend = async () => {
    if (!canResend || !email) return;

    setIsResending(true);
    setError('');

    try {
      await authApiService.resendVerificationOTP(email);

      // Success - start cooldown and clear current code
      startTimer(60);
      setCode('');

    } catch (error) {
      console.error('Resend error:', error);
      if (error instanceof Error) {
        setError(error.message || 'Failed to resend code');
      } else {
        setError('Network error. Please try again.');
      }
    } finally {
      setIsResending(false);
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    onSuccess?.();
  };

  const isCodeComplete = code.length === 6;

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Enter Verification Code
          </h3>
          {email && (
            <p className="text-sm text-gray-600">
              We sent a code to <span className="font-medium">{email}</span>
            </p>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm text-center">
            {error}
          </div>
        )}

        {/* OTP Input */}
        <div className="flex justify-center">
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            onChange={setCode}
            className="gap-3"
          />
        </div>

        {/* Verify Button */}
        <Button
          onClick={handleVerify}
          disabled={!isCodeComplete || isVerifying}
          className="w-full"
          size="lg"
        >
          {isVerifying ? AUTH_LOADING_MESSAGES.VERIFYING_EMAIL : 'Verify'}
        </Button>

        {/* Resend Link */}
        <div className="text-center">
          <span className="text-gray-600">Haven&apos;t received OTP? </span>
          <button
            onClick={handleResend}
            disabled={!canResend || isResending || !email}
            className="text-amber-800 hover:text-amber-900 font-medium disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            {isResending ? AUTH_LOADING_MESSAGES.SENDING_OTP : !canResend ? `Resend (${timeLeft}s)` : 'Resend'}
          </button>
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessModalClose}
        title="Email Verified!"
        buttonText="Continue"
        onButtonClick={handleSuccessModalClose}
      />
    </>
  );
}

export function VerifyEmail({ onSuccess }: VerifyEmailProps) {
  return (
    <Suspense fallback={<div className="text-center">Loading...</div>}>
      <VerifyEmailContent onSuccess={onSuccess} />
    </Suspense>
  );
}
