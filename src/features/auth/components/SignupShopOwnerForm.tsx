"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button, Input } from "@/shared/components/ui";
import {
  useFormValidation,
  createEmailValidation,
  createPasswordValidation,
  createRequiredValidation,
  createPhoneValidation,
} from "@/shared/hooks";
import { ShopOwnerFormData } from "@/types/auth";
import Image from "next/image";
import { authApiService } from "../services/auth-api";
import { AUTH_ROUTES, AUTH_LOADING_MESSAGES } from "../constants";

export function SignupShopOwnerForm() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [apiError, setApiError] = useState<string>("");

  // Use standardized form validation - FIXED VERSION
  const { formState, handleSubmit, getFieldProps, setError } =
    useFormValidation<ShopOwnerFormData>({
      initialData: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "", // Fixed: using 'phone' instead of 'phoneNumber'
        password: "",
        businessName: "",
        vatNumber: "",
      },
      validationRules: {
        firstName: createRequiredValidation("First name"),
        lastName: createRequiredValidation("Last name"),
        email: createEmailValidation(),
        phone: createPhoneValidation(), // FIXED: Use createPhoneValidation instead of createRequiredValidation
        password: createPasswordValidation(),
        businessName: createRequiredValidation("Business name"),
        vatNumber: createRequiredValidation("VAT number"),
      },
      onSubmit: async (data) => {
        setApiError("");
        console.log("Form data before submission:", data);
        console.log("Form validation state:", formState);
        try {
          const result = await authApiService.register(data, "shop_owner");

          if (result && "requiresEmailVerification" in result) {
            router.push(
              `${
                AUTH_ROUTES.VERIFY_EMAIL
              }?type=shop_owner&email=${encodeURIComponent(data.email)}`
            );
            return;
          }

          router.push(
            `${
              AUTH_ROUTES.VERIFY_EMAIL
            }?type=shop_owner&email=${encodeURIComponent(data.email)}`
          );
        } catch (error) {
          console.error("Registration error:", error);

          if (error instanceof Error) {
            try {
              const errorResult = JSON.parse(error.message);
              if (errorResult.details && Array.isArray(errorResult.details)) {
                errorResult.details.forEach(
                  (detail: { field: string; message: string }) => {
                    const fieldName =
                      detail.field === "phoneNumber"
                        ? "phone"
                        : (detail.field as keyof ShopOwnerFormData);
                    setError(fieldName, detail.message);
                  }
                );
              } else {
                setApiError(
                  errorResult.message ||
                    errorResult.error ||
                    "Registration failed"
                );
              }
            } catch {
              setApiError(error.message || "Registration failed");
            }
          } else {
            setApiError("Network error. Please try again.");
          }
        }
      },
    });

  const handleGoogleSignUp = () => {
    console.log("Google sign-up clicked");
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-5">
      {/* API Error Display */}
      {apiError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-red-600 text-sm">{apiError}</p>
        </div>
      )}

      {/* First Name - FIXED: Using getFieldProps pattern */}
      <Input
        type="text"
        name="firstName"
        placeholder="Enter your First Name"
        {...getFieldProps("firstName")}
        onChange={(e) => getFieldProps("firstName").onChange(e.target.value)}
        required
        icon={
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        }
        label="First Name"
      />

      {/* Last Name - FIXED: Using getFieldProps pattern */}
      <Input
        type="text"
        name="lastName"
        placeholder="Enter your Last Name"
        {...getFieldProps("lastName")}
        onChange={(e) => getFieldProps("lastName").onChange(e.target.value)}
        required
        icon={
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        }
        label="Last Name"
      />

      {/* Email - FIXED: Using getFieldProps pattern */}
      <Input
        type="email"
        name="email"
        placeholder="Enter your Email"
        {...getFieldProps("email")}
        onChange={(e) => getFieldProps("email").onChange(e.target.value)}
        required
        icon={
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
            />
          </svg>
        }
        label="Email"
      />

      {/* Phone Number - FIXED: Using getFieldProps pattern */}
      <Input
        type="tel"
        name="phone"
        placeholder="Enter your Phone Number"
        {...getFieldProps("phone")}
        onChange={(e) => getFieldProps("phone").onChange(e.target.value)}
        required
        icon={
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        }
        label="Phone Number"
      />

      {/* Business Name - FIXED: Using getFieldProps pattern */}
      <Input
        type="text"
        name="businessName"
        placeholder="Enter your Business Name"
        {...getFieldProps("businessName")}
        onChange={(e) => getFieldProps("businessName").onChange(e.target.value)}
        required
        icon={
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        }
        label="Business Name"
      />

      {/* VAT Number - FIXED: Using getFieldProps pattern */}
      <Input
        type="text"
        name="vatNumber"
        placeholder="Enter your VAT Number"
        {...getFieldProps("vatNumber")}
        onChange={(e) => getFieldProps("vatNumber").onChange(e.target.value)}
        required
        icon={<span className="text-gray-400 font-medium">#</span>}
        label="VAT Number"
      />

      {/* Password - FIXED: Using getFieldProps pattern */}
      <Input
        type={showPassword ? "text" : "password"}
        name="password"
        placeholder="• • • • • • • •"
        {...getFieldProps("password")}
        onChange={(e) => getFieldProps("password").onChange(e.target.value)}
        required
        icon={
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        }
        rightIcon={
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="hover:text-gray-600"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {showPassword ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              )}
            </svg>
          </button>
        }
        label="Password"
      />

      {/* Submit Button - FIXED: Added proper validation check */}
      <Button
        type="submit"
        className="w-full"
        size="lg"
        disabled={formState.isSubmitting || !formState.isValid}
      >
        {formState.isSubmitting
          ? AUTH_LOADING_MESSAGES.CREATING_ACCOUNT
          : "Register"}
      </Button>

      {/* Divider */}
      <div className="text-center">
        <span className="text-gray-500 text-sm">or Continue with</span>
      </div>

      {/* Google Sign Up */}
      <Button
        type="button"
        variant="outline"
        onClick={handleGoogleSignUp}
        className="w-full flex items-center justify-center gap-3"
        size="lg"
      >
        <Image
          src="/assets/images/google-logo.svg"
          alt="Google"
          width={20}
          height={20}
        />
        Sign up with Google
      </Button>

      {/* Login Link */}
      <div className="text-center">
        <span className="text-gray-600 text-sm">
          Already have an account?{" "}
          <button
            type="button"
            onClick={() => router.push(AUTH_ROUTES.LOGIN)}
            className="text-amber-600 hover:text-amber-700 font-medium"
          >
            Login
          </button>
        </span>
      </div>
    </form>
  );
}
