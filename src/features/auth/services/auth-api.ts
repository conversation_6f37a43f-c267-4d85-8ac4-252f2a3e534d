// Authentication API service
import {
  LoginFormData,
  AuthResponse,
  LoginApiResponse,
  User,
  UserRole,
  ClientFormData,
  BarberFormData,
  ShopOwnerFormData,
  VerifyEmailRequest,
  ResetPasswordRequest,
  RegisterApiResponse
} from '@/types/auth';
import { ApiResponse } from '@/types/api';
import { STORAGE_KEYS } from '@/shared/constants';
import { apiClient } from '@/shared/services';
import { AUTH_ROUTES } from '../constants';

class AuthApiService {

  private getStoredToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  private storeTokens(authResponse: AuthResponse): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, authResponse.accessToken);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, authResponse.refreshToken);
    localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user));
  }

  private clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_DATA);
  }

  async login(credentials: LoginFormData): Promise<AuthResponse | { requiresEmailVerification: boolean; user?: User; message: string }> {
    // ✅ FIXED: Pass credentials directly as the second parameter
    const response = await apiClient.post<LoginApiResponse>(`${AUTH_ROUTES.LOGIN}`, credentials);

    if (response.success && response.data) {
      // Check if email verification is required
      if (response.data.requiresEmailVerification) {
        return {
          requiresEmailVerification: true,
          user: response.data.data?.user,
          message: response.data.message || 'Please verify your email address'
        };
      }

      // For successful login with tokens, they should be in response.data.data
      if (response.data.data) {
        const authData = response.data.data;
        if ('accessToken' in authData && 'refreshToken' in authData && authData.user) {
          const authResponse: AuthResponse = {
            user: authData.user as User,
            accessToken: authData.accessToken as string,
            refreshToken: authData.refreshToken as string,
          };

          this.storeTokens(authResponse);
          return authResponse;
        }
      }
    }

    throw new Error(response.error || response.message || 'Login failed');
  }

  async register(
    userData: ClientFormData | BarberFormData | ShopOwnerFormData,
    role: UserRole
  ): Promise<RegisterApiResponse['data'] | null> {
    // Log the original data for debugging
    console.log('Original form data:', userData);

    const apiData = {
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      phoneNumber: userData.phone,
      password: userData.password,
      role,
      // role-specific fields
      ...('experience' in userData && userData.experience && { experience: userData.experience }),
      ...('vatNumber' in userData && userData.vatNumber && { vatNumber: userData.vatNumber }),
      ...('businessName' in userData && userData.businessName && { businessName: userData.businessName }),
    };

    // Log the API data for debugging
    console.log('API data being sent:', apiData);

    try {
      const response = await apiClient.post<RegisterApiResponse>('/auth/register', apiData);

      // Check if registration was successful
      if (response.success) {
        // If we have user data and tokens, store them
        if (response.data && response.data.data?.user) {
          const authResponse: AuthResponse = {
            user: response.data.data.user,
            accessToken: response.data.data?.accessToken,
            refreshToken: response.data.data?.refreshToken,
          };
          this.storeTokens(authResponse);
          return response.data.data;
        }

        // Registration successful but no tokens (email verification required)
        // This is normal for the registration flow
        console.log('Registration successful, email verification required:', response.message);
        return null;
      }

      throw new Error(response.error || response.message || 'Registration failed');
    } catch (error: unknown) {
      console.error('Registration API error:', error);

      if (error && typeof error === 'object' && 'response' in error) {
        const errorWithResponse = error as { response: { data: unknown } };
        if (errorWithResponse.response && errorWithResponse.response.data) {
          throw new Error(JSON.stringify(errorWithResponse.response.data));
        }
      }

      throw error;
    }
  }

  async verifyEmailOTP(data: VerifyEmailRequest): Promise<AuthResponse | void> {
    // ✅ FIXED: Pass data directly
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/verify-email-otp', data);

    if (response.success) {
      // If verification returns auth tokens, store them
      if (response.data && response.data.data) {
        const authResponse = response.data.data;
        this.storeTokens(authResponse);
        return authResponse;
      }
      // Verification successful but no tokens returned
      return;
    }

    throw new Error(response.error || response.message || 'Email verification failed');
  }

  async resendVerificationOTP(email: string): Promise<void> {
    // ✅ FIXED: Pass object directly
    const response = await apiClient.post<ApiResponse<void>>('/auth/resend-verification-otp', { email });

    if (!response.success) {
      throw new Error(response.error || response.message || 'Failed to resend verification OTP');
    }
  }

  // Legacy method for backward compatibility
  async verifyEmail(data: VerifyEmailRequest): Promise<void> {
    await this.verifyEmailOTP(data);
  }

  async sendOTP(email: string, type: 'password-reset' | 'email-verification'): Promise<void> {
    if (type === 'email-verification') {
      return this.resendVerificationOTP(email);
    }

    // ✅ FIXED: Pass object directly
    const response = await apiClient.post<ApiResponse<void>>('/auth/send-otp', { email, type });

    if (!response.success) {
      throw new Error(response.error || response.message || 'Failed to send OTP');
    }
  }

  async verifyOTP(data: VerifyEmailRequest): Promise<void> {
    await this.verifyEmailOTP(data);
  }

  async resetPassword(data: ResetPasswordRequest & { resetToken: string }): Promise<void> {
    // ✅ FIXED: Pass data directly
    const response = await apiClient.post<ApiResponse<void>>('/auth/reset-password', data);

    if (!response.success) {
      throw new Error(response.error || response.message || 'Password reset failed');
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    if (!refreshToken || refreshToken === 'undefined' || refreshToken === 'null') {
      throw new Error('Invalid refresh token');
    }

    // ✅ FIXED: Pass object directly
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/refresh', { refreshToken });

    if (response.success && response.data && response.data.data) {
      this.storeTokens(response.data.data);
      return response.data.data;
    }

    // Clear invalid tokens on refresh failure
    this.clearTokens();
    throw new Error(response.error || response.message || 'Invalid refresh token');
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post<ApiResponse<void>>('/auth/logout');
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  // Forgot Password API methods
  async requestPasswordReset(email: string): Promise<{ success: boolean; message: string }> {
    // ✅ FIXED: Pass object directly
    const response = await apiClient.post<ApiResponse<{ message: string }>>(`${AUTH_ROUTES.FORGOT_PASSWORD}`, { email });

    return {
      success: response.success,
      message: response.message || 'Password reset OTP sent successfully'
    };
  }

  async resetPasswordWithOTP(email: string, otp: string, newPassword: string): Promise<{ success: boolean; message: string }> {
    // ✅ FIXED: Pass object directly
    const response = await apiClient.post<ApiResponse<{ message: string }>>('/auth/reset-password-otp', { email, otp, newPassword });

    return {
      success: response.success,
      message: response.message || 'Password reset successful'
    };
  }

  async resendPasswordResetOTP(email: string): Promise<{ success: boolean; message: string }> {
    // ✅ FIXED: Pass object directly
    const response = await apiClient.post<ApiResponse<{ message: string }>>('/auth/resend-password-reset-otp', { email });

    return {
      success: response.success,
      message: response.message || 'Password reset OTP sent successfully'
    };
  }

  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;
    
    const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
    
    // Check if userData exists and is not null/undefined
    if (!userData || userData === 'undefined' || userData === 'null') {
      return null;
    }
    
    try {
      return JSON.parse(userData);
    } catch (error) {
      console.error('Failed to parse user data from localStorage:', error);
      // Clear invalid data
      localStorage.removeItem(STORAGE_KEYS.USER_DATA);
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }
}

export const authApiService = new AuthApiService();