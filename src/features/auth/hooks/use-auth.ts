'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { 
  User, 
  LoginFormData, 
  UserRole,
  ClientFormData,
  BarberFormData,
  ShopOwnerFormData, 
  AuthResponse
} from '@/types/auth';
import { authApiService } from '../services/auth-api';
import { AUTH_ROUTES } from '../constants';

interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginFormData) => Promise<void>;
  register: (userData: ClientFormData | BarberFormData | ShopOwnerFormData, role: UserRole) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  refreshToken: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const isAuthenticated = !!user;

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const login = useCallback(async (credentials: LoginFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await authApiService.login(credentials);

      // Check if email verification is required
      if ('requiresEmailVerification' in response && response.requiresEmailVerification) {
        setError(response.message);
        // Redirect to email verification page and trigger OTP sending
        router.push(`${AUTH_ROUTES.VERIFY_EMAIL}?email=${encodeURIComponent(credentials.email)}&sendOtp=true`);
        return;
      }

      // Successful login
      const authResponse = response as AuthResponse;
      setUser(authResponse.user);

      // Redirect to dashboard after successful login
      router.push(AUTH_ROUTES.DASHBOARD);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const register = useCallback(async (
    userData: ClientFormData | BarberFormData | ShopOwnerFormData,
    role: UserRole
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const authResponse = await authApiService.register(userData, role);
      if (authResponse) {
        setUser(authResponse.user);
      }

      // Registration successful - redirect to email verification
      router.push(`${AUTH_ROUTES.VERIFY_EMAIL}?email=${encodeURIComponent(userData.email)}&type=${role}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await authApiService.logout();
      setUser(null);
      router.push(AUTH_ROUTES.HOME);
    } catch (err) {
      console.error('Logout error:', err);
      // Clear local state even if API call fails
      setUser(null);
      router.push(AUTH_ROUTES.HOME);
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  const refreshToken = useCallback(async () => {
    try {
      const authResponse = await authApiService.refreshToken();
      setUser(authResponse.user);
    } catch (err) {
      // Don't log error for invalid refresh token as it's expected behavior
      if (err instanceof Error && !err.message.includes('Invalid refresh token')) {
        console.error('Token refresh failed:', err);
      }
      setUser(null);
      // Don't redirect to login from refresh token failure - let the route protection handle it
    }
  }, []);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        if (authApiService.isAuthenticated()) {
          const currentUser = authApiService.getCurrentUser();
          if (currentUser) {
            setUser(currentUser);
          } else {
            // If token exists but no user data, clear invalid tokens
            console.log('Token exists but no user data found, clearing tokens');
            authApiService.logout();
            setUser(null);
          }
        }
      } catch (err) {
        console.error('Auth initialization failed:', err);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
    refreshToken,
  };
}
