// Auth feature constants - All auth-specific constants in one place

// Auth route constants
export const AUTH_ROUTES = {
  // Authentication pages
  HOME: '/',
  LOGIN: '/auth/login',
  SIGNUP: '/auth/signup',
  VERIFY_EMAIL: '/auth/verify-email',
  FORGOT_PASSWORD: '/auth/forgot-password',
  OTP: '/auth/otp',
  RESET_PASSWORD: '/auth/reset',
  
  // App routes
  DASHBOARD: '/dashboard',
} as const;

// Auth form validation messages
export const AUTH_LOADING_MESSAGES = {
  // Loading messages
  LOGGING_IN: 'Logging you in...',
  CREATING_ACCOUNT: 'Creating your account...',
  SENDING_OTP: 'Sending verification code...',
  VERIFYING_EMAIL: 'Verifying your email...',
  RESETTING_PASSWORD: 'Resetting your password...',
} as const;

// Auth-specific validation rules
export const AUTH_VALIDATION_RULES = {
  EMAIL: {
    REQUIRED: 'Email is required',
    INVALID: 'Please enter a valid email address',
  },
  PASSWORD: {
    REQUIRED: 'Password is required',
    MIN_LENGTH: 'Password must be at least 8 characters long',
    PATTERN: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  },
  FIRST_NAME: {
    REQUIRED: 'First name is required',
    MIN_LENGTH: 'First name must be at least 2 characters long',
  },
  LAST_NAME: {
    REQUIRED: 'Last name is required',
    MIN_LENGTH: 'Last name must be at least 2 characters long',
  },
  PHONE_NUMBER: {
    REQUIRED: 'Phone number is required',
    INVALID: 'Please enter a valid phone number',
  },
  VAT_NUMBER: {
    REQUIRED: 'VAT number is required',
    INVALID: 'Please enter a valid VAT number',
  }
} as const;

