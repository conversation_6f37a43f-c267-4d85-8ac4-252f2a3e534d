'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { ApiResponse } from '@/types/api';

interface UseApiState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  isSuccess: boolean;
}

interface UseApiOptions<T = unknown> {
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
  retryAttempts?: number;
  retryDelay?: number;
}

interface UseApiReturn<T> {
  state: UseApiState<T>;
  execute: (...args: unknown[]) => Promise<T>;
  reset: () => void;
  cancel: () => void;
}

export function useApi<T = unknown>(
  apiFunction: (...args: unknown[]) => Promise<ApiResponse<T> | T>,
  options: UseApiOptions<T> = {}
): UseApiReturn<T> {
  const {
    onSuccess,
    onError,
    retryAttempts = 0,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
    isSuccess: false,
  });

  const cancelRef = useRef<boolean>(false);
  const retryTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
      isSuccess: false,
    });
    cancelRef.current = false;
  }, []);

  const cancel = useCallback(() => {
    cancelRef.current = true;
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    setState(prev => ({
      ...prev,
      isLoading: false,
    }));
  }, []);

  const executeWithRetry = useCallback(async (
    args: unknown[],
    attempt: number = 0
  ): Promise<T> => {
    if (cancelRef.current) {
      throw new Error('Request cancelled');
    }

    try {
      const result = await apiFunction(...args);
      
      if (cancelRef.current) {
        throw new Error('Request cancelled');
      }

      // Handle ApiResponse format
      let data: T;
      if (result && typeof result === 'object' && 'success' in result) {
        const apiResponse = result as ApiResponse<T>;
        if (!apiResponse.success) {
          throw new Error(apiResponse.error || 'API request failed');
        }
        data = apiResponse.data as T;
      } else {
        data = result as T;
      }

      setState({
        data,
        isLoading: false,
        error: null,
        isSuccess: true,
      });

      onSuccess?.(data);
      return data;
    } catch (error) {
      if (cancelRef.current) {
        return Promise.reject(new Error('Request cancelled'));
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // Retry logic
      if (attempt < retryAttempts) {
        return new Promise((resolve, reject) => {
          retryTimeoutRef.current = setTimeout(() => {
            executeWithRetry(args, attempt + 1)
              .then(resolve)
              .catch(reject);
          }, retryDelay * Math.pow(2, attempt)); // Exponential backoff
        });
      }

      setState({
        data: null,
        isLoading: false,
        error: errorMessage,
        isSuccess: false,
      });

      onError?.(errorMessage);
      throw error;
    }
  }, [apiFunction, onSuccess, onError, retryAttempts, retryDelay]);

  const execute = useCallback(async (...args: unknown[]): Promise<T> => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      isSuccess: false,
    }));

    cancelRef.current = false;
    return executeWithRetry(args);
  }, [executeWithRetry]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancel();
    };
  }, [cancel]);

  return {
    state,
    execute,
    reset,
    cancel,
  };
}

// Specialized hook for mutations (POST, PUT, DELETE)
export function useMutation<T = unknown, TVariables = unknown>(
  mutationFunction: (variables: TVariables) => Promise<ApiResponse<T> | T>,
  options: UseApiOptions<T> = {}
) {
  const api = useApi<T>(mutationFunction as (...args: unknown[]) => Promise<ApiResponse<T> | T>, options);

  const mutate = useCallback(async (variables: TVariables): Promise<T> => {
    return api.execute(variables);
  }, [api]);

  return {
    ...api.state,
    mutate,
    reset: api.reset,
    cancel: api.cancel,
  };
}

// Specialized hook for queries (GET)
export function useQuery<T = unknown>(
  queryFunction: () => Promise<ApiResponse<T> | T>,
  options: UseApiOptions<T> & {
    enabled?: boolean;
    refetchOnMount?: boolean;
    refetchInterval?: number;
  } = {}
) {
  const {
    enabled = true,
    refetchOnMount = true,
    refetchInterval,
    ...apiOptions
  } = options;

  const api = useApi<T>(queryFunction, apiOptions);
  const intervalRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const refetch = useCallback(() => {
    if (enabled) {
      return api.execute();
    }
    return Promise.resolve(null as T);
  }, [api, enabled]);

  // Auto-fetch on mount
  useEffect(() => {
    if (enabled && refetchOnMount) {
      refetch();
    }
  }, [enabled, refetchOnMount, refetch]);

  // Set up refetch interval
  useEffect(() => {
    if (refetchInterval && enabled) {
      intervalRef.current = setInterval(refetch, refetchInterval);
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [refetchInterval, enabled, refetch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    ...api.state,
    refetch,
    reset: api.reset,
    cancel: api.cancel,
  };
}
