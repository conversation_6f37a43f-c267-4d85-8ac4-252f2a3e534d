'use client';

import { cn } from '@/lib/utils';

export interface Tab {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

export interface TabSelectorProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export function TabSelector({ tabs, activeTab, onTabChange, className }: TabSelectorProps) {
  return (
    <div className={cn('flex bg-white/10 backdrop-blur-md rounded-2xl p-2 gap-2 border border-white/20', className)}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={cn(
            'flex-1 flex flex-col items-center justify-center py-4 px-3 rounded-xl transition-all duration-300',
            'text-sm font-medium min-h-[80px]',
            activeTab === tab.id
              ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/30'
              : 'text-white/70 hover:text-white hover:bg-white/10 border border-transparent'
          )}
        >
          {tab.icon && (
            <div className="mb-2 opacity-90">
              {tab.icon}
            </div>
          )}
          <span className="text-xs lg:text-sm">{tab.label}</span>
        </button>
      ))}
    </div>
  );
}
