'use client';

import React from 'react';
import { Button } from './button';
import { CheckIcon } from '@/shared/components/icons';

export interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message?: string;
  buttonText?: string;
  onButtonClick?: () => void;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  buttonText = 'Continue',
  onButtonClick,
}) => {
  if (!isOpen) return null;

  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    } else {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-2xl p-8 mx-4 max-w-sm w-full shadow-xl">
        <div className="text-center">
          {/* Success Icon */}
          <div className="mx-auto mb-6 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckIcon className="text-green-600" size="lg" />
          </div>
          
          {/* Title */}
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {title}
          </h3>
          
          {/* Message */}
          {message && (
            <p className="text-gray-600 mb-6">
              {message}
            </p>
          )}
          
          {/* Button */}
          <Button
            onClick={handleButtonClick}
            className="w-full"
            size="lg"
          >
            {buttonText}
          </Button>
        </div>
      </div>
    </div>
  );
};

export { SuccessModal };
