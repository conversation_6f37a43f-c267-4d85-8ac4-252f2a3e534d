'use client';

import { useState, useEffect } from 'react';
import { Button, OTPInput, SuccessModal } from '@/shared/components/ui';
import { useResendTimer } from '@/features/auth/hooks/use-resend-timer';
import { AUTH_LOADING_MESSAGES } from '@/features/auth/constants';

export interface OTPVerificationProps {
  // Required props
  email: string;
  onVerify: (otp: string) => Promise<void>;
  onResend: () => Promise<void>;
  
  // Optional customization
  title?: string;
  description?: string;
  verifyButtonText?: string;
  successTitle?: string;
  successButtonText?: string;
  onSuccess?: () => void;
  
  // Auto-send OTP on mount (for login flow)
  autoSendOtp?: boolean;
  
  // Timer duration in seconds
  timerDuration?: number;
  
  // Custom loading states
  isVerifying?: boolean;
  isResending?: boolean;
  
  // External error handling
  error?: string;
  onErrorChange?: (error: string) => void;
}

export function OTPVerification({
  email,
  onVerify,
  onResend,
  title = "Enter Verification Code",
  description,
  verifyButtonText = "Verify",
  successTitle = "Verification Successful!",
  successButtonText = "Continue",
  onSuccess,
  autoSendOtp = false,
  timerDuration = 60,
  isVerifying = false,
  isResending = false,
  error,
  onErrorChange,
}: OTPVerificationProps) {
  const [code, setCode] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [internalError, setInternalError] = useState<string>('');
  
  // Use external error if provided, otherwise use internal error
  const displayError = error || internalError;
  const setDisplayError = onErrorChange || setInternalError;

  // Use the shared resend timer hook
  const { timeLeft, canResend, startTimer } = useResendTimer(timerDuration);

  // Auto-send OTP when component mounts (for login flow)
  useEffect(() => {
    if (autoSendOtp && email) {
      handleResend();
    }
  }, []); // Only run on mount

  // Start timer on component mount if auto-send is enabled
  useEffect(() => {
    if (autoSendOtp && email) {
      startTimer(timerDuration);
    }
  }, [autoSendOtp, email, startTimer, timerDuration]);

  const handleOTPComplete = (otpValue: string) => {
    setCode(otpValue);
    setDisplayError(''); // Clear error when user types
  };

  const handleVerify = async () => {
    if (code.length !== 6) return;

    setDisplayError('');

    try {
      await onVerify(code);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Verification error:', error);
      if (error instanceof Error) {
        setDisplayError(error.message || 'Verification failed');
      } else {
        setDisplayError('Network error. Please try again.');
      }
    }
  };

  const handleResend = async () => {
    if (!canResend || !email) return;

    setDisplayError('');

    try {
      await onResend();
      // Success - start cooldown and clear current code
      startTimer(timerDuration);
      setCode('');
    } catch (error) {
      console.error('Resend error:', error);
      if (error instanceof Error) {
        setDisplayError(error.message || 'Failed to resend code');
      } else {
        setDisplayError('Network error. Please try again.');
      }
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    onSuccess?.();
  };

  const isCodeComplete = code.length === 6;

  // Generate description if not provided
  const finalDescription = description || `We sent a code to ${email}`;

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {title}
          </h3>
          {email && (
            <p className="text-sm text-gray-600">
              {finalDescription}
            </p>
          )}
        </div>

        {/* Error Display */}
        {displayError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm text-center">
            {displayError}
          </div>
        )}

        {/* OTP Input */}
        <div className="flex justify-center">
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            onChange={setCode}
            className="gap-3"
          />
        </div>

        {/* Verify Button */}
        <Button
          onClick={handleVerify}
          disabled={!isCodeComplete || isVerifying}
          className="w-full"
          size="lg"
        >
          {isVerifying ? AUTH_LOADING_MESSAGES.VERIFYING_EMAIL : verifyButtonText}
        </Button>

        {/* Resend Link */}
        <div className="text-center">
          <span className="text-gray-600">Haven&apos;t received OTP? </span>
          <button
            onClick={handleResend}
            disabled={!canResend || isResending || !email}
            className="text-amber-800 hover:text-amber-900 font-medium disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            {isResending ? AUTH_LOADING_MESSAGES.SENDING_OTP : !canResend ? `Resend (${timeLeft}s)` : 'Resend'}
          </button>
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessModalClose}
        title={successTitle}
        buttonText={successButtonText}
        onButtonClick={handleSuccessModalClose}
      />
    </>
  );
}
