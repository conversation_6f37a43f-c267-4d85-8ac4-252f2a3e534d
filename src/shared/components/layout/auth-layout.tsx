'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { AuthLayoutProps } from '@/types/ui';

const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title,
  showBackButton = false,
  onBack,
  className,
  heroContent,
  variant = 'default',
  contentType = 'simple',
}) => {
  const isHomePage = variant === 'home';
  const isComplexContent = contentType === 'complex'; // signup with tabs

  return (
    <div className={cn(
      "bg-gray-50 flex flex-col lg:flex-row",
      isHomePage ? "h-screen overflow-hidden" : "min-h-screen"
    )}>
      {/* Hero Background Section */}
      <div className={cn(
        "relative lg:w-1/2 lg:h-screen", // Ensure full screen height on desktop
        isHomePage
          ? "h-[75vh]"
          : isComplexContent
            ? "h-[40vh]" // More space for signup with tabs
            : "h-[30vh]"  // Less space for simple forms like login
      )}>
        {/* Background Image with curved bottom - only on home page */}
        <div
          className={cn(
            "absolute inset-0 bg-cover bg-center bg-no-repeat lg:rounded-none",
            isHomePage ? "rounded-b-[3rem]" : ""
          )}
          style={{
            backgroundImage: `url('/assets/images/barber-hero.jpg')`,
          }}
        />

        {/* Gradient overlay to prevent whitening and improve text readability */}
        <div className={cn(
          "absolute inset-0 bg-gradient-to-b from-black/20 via-black/10 to-black/30",
          isHomePage ? "rounded-b-[3rem]" : ""
        )} />

        {/* Header */}
        <div className="relative z-10 flex items-center justify-between p-4 pt-12 lg:pt-8">
          {showBackButton && (
            <button
              onClick={onBack}
              className="p-2 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}

          <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center lg:hidden">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </div>
        </div>

        {/* Hero Content */}
        <div className={cn(
          "absolute left-4 right-4 z-10",
          isHomePage
            ? "bottom-8 lg:bottom-1/2 lg:transform lg:translate-y-1/2"
            : isComplexContent
              ? "lg:top-1/2 lg:transform lg:-translate-y-1/2" // More space for tabs
              : "lg:top-1/2 lg:transform lg:-translate-y-1/2"  // Simple forms
        )}>
          {heroContent ? (
            heroContent
          ) : title ? (
            <div className="text-center">
              <h2 className="text-white text-3xl lg:text-4xl font-bold">{title}</h2>
            </div>
          ) : (
            <h2 className="text-white text-4xl lg:text-5xl font-bold leading-tight text-center lg:text-left">
              Everything<br />
              You Need.<br />
              Nothing<br />
              You Don&apos;t.
            </h2>
          )}
        </div>
      </div>

      {/* Content Section */}
      <div className={cn(
        "flex-1 bg-white relative z-20 lg:w-1/2",
        isHomePage
          ? "lg:flex lg:items-center lg:justify-center"
          : "rounded-t-[2rem] -mt-6 lg:mt-0 lg:rounded-none",
        !isHomePage ? "pb-8 lg:pb-0" : ""
      )}>
        {/* Desktop: Full height scrollable container */}
        <div className={cn(
          'w-full',
          isHomePage
            ? 'p-6 pt-8 lg:p-8 lg:max-w-md lg:w-full'
            : cn(
                'p-6 pt-8 lg:p-8',
                isComplexContent
                  ? 'lg:py-8 lg:max-w-lg lg:mx-auto lg:h-full lg:overflow-y-auto' // Complex forms need scrolling
                  : 'lg:py-12 lg:max-w-lg lg:mx-auto lg:min-h-full lg:flex lg:flex-col lg:justify-center' // Simple forms centered
              ),
          className
        )}>
          {children}
        </div>
      </div>
    </div>
  );
};

export { AuthLayout };
export type { AuthLayoutProps };