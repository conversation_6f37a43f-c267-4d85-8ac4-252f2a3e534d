// Base API client service
import { ApiResponse, ApiError } from '@/types/api';
import { API_CONFIG, STORAGE_KEYS, ERROR_MESSAGES } from '@/shared/constants';
import { AUTH_ROUTES } from '@/features/auth/constants';

export interface RequestConfig extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

class ApiClient {
  private baseUrl: string;
  private defaultTimeout: number;
  private defaultRetries: number;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || API_CONFIG.BASE_URL;
    this.defaultTimeout = API_CONFIG.TIMEOUT;
    this.defaultRetries = API_CONFIG.RETRY_ATTEMPTS;
  }

  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    let data: unknown;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch {
      throw new Error('Failed to parse response');
    }

    if (!response.ok) {
      // Backend sends error in 'error' field, not 'message'
      const errorData = data as { error?: string; message?: string; code?: string; details?: Record<string, unknown>; stack?: string };
      const apiError: ApiError = {
        message: errorData.error || errorData.message || ERROR_MESSAGES.GENERIC,
        code: errorData.code,
        details: errorData.details,
        stack: errorData.stack, // Include stack trace for debugging
      };

      // Handle specific error cases
      switch (response.status) {
        case 401:
          apiError.message = ERROR_MESSAGES.UNAUTHORIZED;
          // 🔧 FIXED: Only handle unauthorized for protected routes, not login/register
          if (this.shouldHandleUnauthorized()) {
            this.handleUnauthorized();
          }
          break;
        case 403:
          apiError.message = ERROR_MESSAGES.FORBIDDEN;
          break;
        case 404:
          apiError.message = ERROR_MESSAGES.NOT_FOUND;
          break;
        case 422:
          apiError.message = ERROR_MESSAGES.VALIDATION_ERROR;
          break;
        case 500:
          apiError.message = ERROR_MESSAGES.SERVER_ERROR;
          break;
        default:
          if (!apiError.message) {
            apiError.message = ERROR_MESSAGES.GENERIC;
          }
      }

      return {
        success: false,
        error: apiError.message,  // Use 'error' field to match backend
        stack: errorData.stack,        // Include stack for debugging
        data: null,
      };
    }

    const successData = data as { message?: string; data?: T };
    return {
      success: true,
      message: successData.message,      // Use 'message' field for success
      data: successData.data || (data as T),    // Handle both wrapped and unwrapped data
    };
  }

  // 🔧 FIXED: Only handle unauthorized for protected routes
  private shouldHandleUnauthorized(): boolean {
    if (typeof window === 'undefined') return false;
    
    const pathname = window.location.pathname;
    const authRoutes = [`${AUTH_ROUTES.LOGIN}`, `${AUTH_ROUTES.SIGNUP}`, `${AUTH_ROUTES.FORGOT_PASSWORD}`];
    
    // Don't handle unauthorized on auth routes
    return !authRoutes.some(route => pathname.includes(route));
  }

  private handleUnauthorized(): void {
    // Clear stored tokens
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_DATA);
    }

    // Only redirect if not already on login page
    if (typeof window !== 'undefined' && window.location.pathname !== `${AUTH_ROUTES.LOGIN}`) {
      window.location.href = `${AUTH_ROUTES.LOGIN}`;
    }
  }

  private async requestWithTimeout(
    url: string,
    config: RequestConfig
  ): Promise<Response> {
    const { timeout = this.defaultTimeout, ...requestConfig } = config;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...requestConfig,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  private async requestWithRetry<T>(
    url: string,
    config: RequestConfig,
    attempt: number = 0
  ): Promise<ApiResponse<T>> {
    const { retries = this.defaultRetries, retryDelay = 1000, ...requestConfig } = config;

    try {
      const response = await this.requestWithTimeout(url, requestConfig);
      return this.handleResponse<T>(response);
    } catch (error) {
      const isLastAttempt = attempt >= retries;
      const isRetryableError = error instanceof Error && 
        (error.message.includes('fetch') || error.message.includes('network'));

      if (!isLastAttempt && isRetryableError) {
        // Exponential backoff
        const delay = retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.requestWithRetry<T>(url, config, attempt + 1);
      }

      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.NETWORK,
      };
    }
  }

  async request<T = unknown>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getAuthToken();

    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`;
    }

    const requestConfig: RequestConfig = {
      ...config,
      headers: {
        ...defaultHeaders,
        ...config.headers,
      },
    };

    return this.requestWithRetry<T>(url, requestConfig);
  }

  async get<T = unknown>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T = unknown>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = unknown>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T = unknown>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = unknown>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // File upload method
  async uploadFile<T = unknown>(
    endpoint: string,
    file: File,
    additionalData?: Record<string, unknown>,
    config?: Omit<RequestConfig, 'body' | 'headers'>
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    const token = this.getAuthToken();
    const headers: Record<string, string> = {};
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: formData,
      headers,
    });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Export the class for testing or custom instances
export { ApiClient };