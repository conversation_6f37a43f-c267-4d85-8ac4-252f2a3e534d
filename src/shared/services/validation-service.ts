/**
 * Validation service for form validation and data validation
 * Provides comprehensive validation utilities with consistent error handling
 */
import { AUTH_VALIDATION_RULES } from '@/features/auth/constants';
import {
  VALIDATION_PATTERNS,
  VALIDATION_ERROR_TEMPLATES,
  FILE_SIZE_LIMITS,
  VALIDATION_LENGTHS,
  PASSWORD_STRENGTH
} from '@/shared/constants/validation';

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: unknown) => string | null;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  numeric?: boolean;
  min?: number;
  max?: number;
}

export interface ValidationSchema {
  [field: string]: ValidationRule;
}

/**
 * ValidationService provides comprehensive form and data validation
 * with consistent error handling and reusable validation patterns
 */
class ValidationService {
  // Use centralized validation patterns
  private readonly emailRegex = VALIDATION_PATTERNS.EMAIL;
  private readonly phoneRegex = VALIDATION_PATTERNS.PHONE;
  private readonly urlRegex = VALIDATION_PATTERNS.URL;
  private readonly numericRegex = VALIDATION_PATTERNS.NUMERIC;

  /**
   * Validates a single field value against a validation rule
   * @param value - The value to validate
   * @param rule - The validation rule to apply
   * @param fieldName - The name of the field for error messages
   * @returns Error message if validation fails, null if valid
   */
  validateField(value: unknown, rule: ValidationRule, fieldName: string): string | null {
    // Required validation
    if (rule.required && this.isEmpty(value)) {
      return VALIDATION_ERROR_TEMPLATES.REQUIRED(fieldName);
    }

    // Skip other validations if value is empty and not required
    if (this.isEmpty(value) && !rule.required) {
      return null;
    }

    const stringValue = this.safeStringify(value);

    // Length validations with null safety
    if (rule.minLength && stringValue.length < rule.minLength) {
      return VALIDATION_ERROR_TEMPLATES.MIN_LENGTH(fieldName, rule.minLength);
    }

    if (rule.maxLength && stringValue.length > rule.maxLength) {
      return VALIDATION_ERROR_TEMPLATES.MAX_LENGTH(fieldName, rule.maxLength);
    }

    // Pattern validation with null safety
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return VALIDATION_ERROR_TEMPLATES.INVALID_FORMAT(fieldName);
    }

    // Email validation - use AUTH_VALIDATION_RULES for consistency
    if (rule.email && !this.emailRegex.test(stringValue)) {
      return AUTH_VALIDATION_RULES.EMAIL.INVALID;
    }

    // Phone validation - use AUTH_VALIDATION_RULES for consistency
    if (rule.phone && !this.phoneRegex.test(stringValue)) {
      return AUTH_VALIDATION_RULES.PHONE_NUMBER.INVALID;
    }

    // URL validation
    if (rule.url && !this.urlRegex.test(stringValue)) {
      return VALIDATION_ERROR_TEMPLATES.INVALID_URL(fieldName);
    }

    // Numeric validation
    if (rule.numeric && !this.numericRegex.test(stringValue)) {
      return VALIDATION_ERROR_TEMPLATES.INVALID_NUMBER(fieldName);
    }

    // Min/Max value validation with null safety
    const numericValue = this.safeNumberify(value);
    if (rule.min !== undefined && !isNaN(numericValue) && numericValue < rule.min) {
      return VALIDATION_ERROR_TEMPLATES.MIN_VALUE(fieldName, rule.min);
    }

    if (rule.max !== undefined && !isNaN(numericValue) && numericValue > rule.max) {
      return VALIDATION_ERROR_TEMPLATES.MAX_VALUE(fieldName, rule.max);
    }

    // Custom validation
    if (rule.custom) {
      return rule.custom(value);
    }

    return null;
  }

  /**
   * Validates an entire object against a validation schema
   * @param data - The data object to validate
   * @param schema - The validation schema to apply
   * @returns ValidationResult with isValid flag and errors object
   */
  validateObject(data: Record<string, unknown>, schema: ValidationSchema): ValidationResult {
    const errors: Record<string, string> = {};

    Object.entries(schema).forEach(([fieldName, rule]) => {
      const value = data[fieldName];
      const error = this.validateField(value, rule, fieldName);

      if (error) {
        errors[fieldName] = error;
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  /**
   * Checks if a value is considered empty for validation purposes
   * Enhanced with better null safety and type checking
   * @param value - The value to check
   * @returns True if the value is empty, false otherwise
   */
  private isEmpty(value: unknown): boolean {
    // Null and undefined checks
    if (value === null || value === undefined) return true;

    // String checks with null safety
    if (typeof value === 'string') {
      return value.trim() === '';
    }

    // Array checks with null safety
    if (Array.isArray(value)) {
      return value.length === 0;
    }

    // Object checks with null safety (excluding null which is already handled)
    if (typeof value === 'object' && value !== null) {
      return Object.keys(value).length === 0;
    }

    // Boolean false, 0, etc. are considered valid values
    return false;
  }

  /**
   * Returns validation schema for email fields
   */
  getEmailSchema(): ValidationSchema {
    return {
      email: {
        required: true,
        email: true,
      },
    };
  }

  /**
   * Returns validation schema for password fields
   * Uses centralized pattern and eliminates duplication
   */
  getPasswordSchema(): ValidationSchema {
    return {
      password: {
        required: true,
        minLength: VALIDATION_LENGTHS.PASSWORD_MIN,
        pattern: VALIDATION_PATTERNS.PASSWORD,
        custom: (value: unknown) => {
          if (!value || typeof value !== 'string') return null; // Required validation handles empty values
          if (!VALIDATION_PATTERNS.PASSWORD.test(value)) {
            return AUTH_VALIDATION_RULES.PASSWORD.PATTERN;
          }
          return null;
        },
      },
    };
  }

  /**
   * Returns validation schema for name fields
   */
  getNameSchema(): ValidationSchema {
    return {
      firstName: {
        required: true,
        minLength: VALIDATION_LENGTHS.NAME_MIN,
        maxLength: VALIDATION_LENGTHS.NAME_MAX,
      },
      lastName: {
        required: true,
        minLength: VALIDATION_LENGTHS.NAME_MIN,
        maxLength: VALIDATION_LENGTHS.NAME_MAX,
      },
    };
  }

  /**
   * Returns validation schema for phone number fields
   */
  getPhoneSchema(): ValidationSchema {
    return {
      phoneNumber: {
        required: true,
        phone: true,
      },
    };
  }

  /**
   * Validates file upload with size and type constraints
   * Uses centralized constants for file size calculations
   * @param file - The file to validate
   * @param options - Validation options including size and type constraints
   * @returns Error message if validation fails, null if valid
   */
  validateFile(
    file: File,
    options: {
      maxSize?: number;
      allowedTypes?: string[];
      allowedExtensions?: string[];
    } = {}
  ): string | null {
    const { maxSize, allowedTypes, allowedExtensions } = options;

    // File size validation using centralized constants
    if (maxSize && file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / FILE_SIZE_LIMITS.BYTES_PER_MB);
      return VALIDATION_ERROR_TEMPLATES.FILE_TOO_LARGE(maxSizeMB);
    }

    // File type validation
    if (allowedTypes && !allowedTypes.includes(file.type)) {
      return VALIDATION_ERROR_TEMPLATES.FILE_TYPE_NOT_ALLOWED(file.type);
    }

    // File extension validation
    if (allowedExtensions) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
        return VALIDATION_ERROR_TEMPLATES.FILE_EXTENSION_NOT_ALLOWED(allowedExtensions);
      }
    }

    return null;
  }

  /**
   * Checks password strength and provides feedback
   * @param password - The password to check
   * @returns Object with score (0-5) and feedback array
   */
  checkPasswordStrength(password: string): {
    score: number; // 0-5
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = PASSWORD_STRENGTH.MIN_SCORE;

    // Length check
    if (password.length >= VALIDATION_LENGTHS.PASSWORD_MIN) score++;
    else feedback.push(`Use at least ${VALIDATION_LENGTHS.PASSWORD_MIN} characters`);

    // Lowercase letters
    if (/[a-z]/.test(password)) score++;
    else feedback.push('Include lowercase letters');

    // Uppercase letters
    if (/[A-Z]/.test(password)) score++;
    else feedback.push('Include uppercase letters');

    // Numbers
    if (/\d/.test(password)) score++;
    else feedback.push('Include numbers');

    // Special characters
    if (/[^a-zA-Z\d]/.test(password)) score++;
    else feedback.push('Include special characters');

    return { score, feedback };
  }

  /**
   * Safely converts value to string with null checks
   * @param value - The value to convert
   * @returns String representation or empty string if null/undefined
   */
  private safeStringify(value: unknown): string {
    if (value === null || value === undefined) return '';
    return String(value);
  }

  /**
   * Safely converts value to number with null checks
   * @param value - The value to convert
   * @returns Number representation or NaN if invalid
   */
  private safeNumberify(value: unknown): number {
    if (value === null || value === undefined || value === '') return NaN;
    return Number(value);
  }

  /**
   * Validates that a value is a valid string and not empty
   * @param value - The value to check
   * @returns True if valid non-empty string
   */
  isValidString(value: unknown): value is string {
    return typeof value === 'string' && value.trim().length > 0;
  }

  /**
   * Validates that a value is a valid number
   * @param value - The value to check
   * @returns True if valid number
   */
  isValidNumber(value: unknown): value is number {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
  }

  /**
   * Validates that a value is a valid email format
   * @param value - The value to check
   * @returns True if valid email format
   */
  isValidEmail(value: unknown): boolean {
    return this.isValidString(value) && VALIDATION_PATTERNS.EMAIL.test(value);
  }

  /**
   * Validates that a value is a valid phone number format
   * @param value - The value to check
   * @returns True if valid phone format
   */
  isValidPhone(value: unknown): boolean {
    return this.isValidString(value) && VALIDATION_PATTERNS.PHONE.test(value.replace(/\s/g, ''));
  }
}

export const validationService = new ValidationService();
export { ValidationService };
