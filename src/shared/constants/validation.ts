/**
 * Validation constants including regex patterns, magic numbers, and configuration values
 */

// Regex patterns for validation
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  URL: /^https?:\/\/.+/,
  NUMERIC: /^\d+(\.\d+)?$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
  ZIP_CODE: /^\d{5}(-\d{4})?$/,
  // Additional patterns for future use
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  LETTERS_ONLY: /^[a-zA-Z\s]+$/,
  NUMBERS_ONLY: /^\d+$/,
} as const;

// Note: FILE_SIZE_LIMITS removed as they were unused.
// File validation will be implemented when file upload features are added.

// Validation length constraints
export const VALIDATION_LENGTHS = {
  PASSWORD_MIN: 8,
  PASSWORD_MAX: 128,
  NAME_MIN: 2,
  NAME_MAX: 50,
  ADDRESS_STREET_MIN: 5,
  _STREET_MAX: 100,
  ADDRESS_CITY_MIN: 2,
  ADDRESS_CITY_MAX: 50,
  ADDRESS_STATE_MIN: 2,
  ADDRESS_STATE_MAX: 50,
  PHONE_MIN: 10,
  PHONE_MAX: 15,
  EMAIL_MAX: 254,
} as const;

// Note: PASSWORD_STRENGTH removed as it was unused.
// Password strength scoring will be implemented when needed.

// Note: FILE_VALIDATION removed as it was unused.
// File validation constants will be added when file upload features are implemented.

// Note: VALIDATION_ERROR_TEMPLATES removed as they were unused.
// Error messages are defined inline in validation functions for better maintainability.

// Note: VALIDATION_CONFIGS removed as they were unused.
// Validation configurations will be added when file upload features are implemented.
