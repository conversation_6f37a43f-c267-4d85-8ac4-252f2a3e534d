/**
 * Validation constants including regex patterns, magic numbers, and configuration values
 */

// Regex patterns for validation - Only keeping actually used patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
  // Note: URL, NUMERIC, ZIP_CODE, ALPHANUMERIC, LETTERS_ONLY, NUMBERS_ONLY removed as unused
} as const;

// Note: FILE_SIZE_LIMITS removed as they were unused.
// File validation will be implemented when file upload features are added.

// Validation length constraints - Only keeping actually used constants
export const VALIDATION_LENGTHS = {
  PASSWORD_MIN: 8,
  // Note: PASSWORD_MAX, NAME_MIN/MAX, ADDRESS_*, PHONE_*, EMAIL_MAX removed as unused
} as const;

// Note: PASSWORD_STRENGTH removed as it was unused.
// Password strength scoring will be implemented when needed.

// Note: FILE_VALIDATION removed as it was unused.
// File validation constants will be added when file upload features are implemented.

// Note: VALIDATION_ERROR_TEMPLATES removed as they were unused.
// Error messages are defined inline in validation functions for better maintainability.

// Note: VALIDATION_CONFIGS removed as they were unused.
// Validation configurations will be added when file upload features are implemented.
