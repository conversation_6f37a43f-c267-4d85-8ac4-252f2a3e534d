// Central export for all types
export * from './auth';
export * from './ui';
export * from './api';

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Unified Form and Validation Types
export interface FormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Hook-based validation rule (function-based)
export type ValidationRule<T> = (value: T) => string | undefined;
export type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule<T[K]>;
};

// Note: Service-based validation types removed as they were unused

// Generic callback types
export type VoidCallback = () => void;
export type ValueCallback<T> = (value: T) => void;
export type EventCallback<T = Event> = (event: T) => void;
