// API related types - Standardized API response structure

// Standard API response structure matching your backend
export interface ApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T | null;
  error?: string | null;
  stack?: string;
}

// Error response details
export interface ApiError {
  code?: string;
  message: string;
  details?: Record<string, unknown>;
  stack?: string;
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// File upload
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}